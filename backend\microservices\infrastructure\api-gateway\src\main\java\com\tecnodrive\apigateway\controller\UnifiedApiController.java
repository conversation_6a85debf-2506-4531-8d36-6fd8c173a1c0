package com.tecnodrive.apigateway.controller;

import java.util.HashMap;
import java.util.Map;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

/**
 * Unified API Controller for Frontend Integration
 * Routes requests to appropriate microservices
 */
@RestController
@RequestMapping("/api/v1")
@CrossOrigin(origins = "*")
public class UnifiedApiController {

    // Constants
    private static final String STATUS_KEY = "status";
    private static final String MESSAGE_KEY = "message";
    private static final String ERROR_VALUE = "error";
    private static final String TENANT_HEADER = "X-Tenant-ID";

    private static final String authServiceUrl = "http://auth-service:8081";
    private static final String walletServiceUrl = "http://wallet-service:8083";
    private static final String parcelServiceUrl = "http://parcel-service:8087";
    private static final String rideServiceUrl = "http://ride-service:8082";
    @SuppressWarnings("unused")
    private static final String locationServiceUrl = "http://location-service:8085";

    private final RestTemplate restTemplate;

    public UnifiedApiController(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    // Helper method to create error response
    private Map<String, Object> createErrorResponse(String message) {
        Map<String, Object> errorResponse = new HashMap<>();
        errorResponse.put(STATUS_KEY, ERROR_VALUE);
        errorResponse.put(MESSAGE_KEY, message);
        return errorResponse;
    }

    // Helper method to safely cast response body
    @SuppressWarnings("unchecked")
    private Map<String, Object> castResponseBody(Object responseBody) {
        return (Map<String, Object>) responseBody;
    }

    // =====================================================
    // AUTHENTICATION ENDPOINTS
    // =====================================================

    @PostMapping("/auth/login")
    public ResponseEntity<Map<String, Object>> login(@RequestBody Map<String, String> credentials) {
        try {
            ResponseEntity<Map> response = restTemplate.postForEntity(
                    AUTH_SERVICE_URL + "/api/test/login",
                    credentials,
                    Map.class);
            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
            return ResponseEntity.ok(responseBody);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put(STATUS_KEY, ERROR_VALUE);
            errorResponse.put(MESSAGE_KEY, "Authentication service unavailable");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    @GetMapping("/auth/users")
    public ResponseEntity<Map<String, Object>> getUsers(@RequestHeader(TENANT_HEADER) String tenantId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    AUTH_SERVICE_URL + "/api/test/users",
                    HttpMethod.GET,
                    entity,
                    Map.class);
            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
            return ResponseEntity.ok(responseBody);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put(STATUS_KEY, ERROR_VALUE);
            errorResponse.put(MESSAGE_KEY, "Failed to fetch users");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    // =====================================================
    // WALLET ENDPOINTS
    // =====================================================

    @GetMapping("/wallets/customer/{customerId}")
    public ResponseEntity<Map<String, Object>> getWalletByCustomer(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @PathVariable String customerId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    WALLET_SERVICE_URL + "/api/v1/wallets/customer/" + customerId,
                    HttpMethod.GET,
                    entity,
                    Map.class);
            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
            return ResponseEntity.ok(responseBody);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Wallet service unavailable");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    @PostMapping("/wallets/{walletId}/top-up")
    public ResponseEntity<Map<String, Object>> topUpWallet(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @PathVariable String walletId,
            @RequestBody Map<String, Object> topUpRequest) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(topUpRequest, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    WALLET_SERVICE_URL + "/api/v1/wallets/" + walletId + "/top-up",
                    HttpMethod.POST,
                    entity,
                    Map.class);
            return ResponseEntity.ok(response.getBody());
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Failed to process top-up");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    @PostMapping("/wallets/{walletId}/payment")
    public ResponseEntity<Map<String, Object>> processPayment(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @PathVariable String walletId,
            @RequestBody Map<String, Object> paymentRequest) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(paymentRequest, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    WALLET_SERVICE_URL + "/api/v1/wallets/" + walletId + "/payment",
                    HttpMethod.POST,
                    entity,
                    Map.class);
            return ResponseEntity.ok(response.getBody());
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Failed to process payment");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    // =====================================================
    // PARCEL ENDPOINTS
    // =====================================================

    @PostMapping("/parcels")
    public ResponseEntity<Map<String, Object>> createParcel(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @RequestBody Map<String, Object> parcelRequest) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(parcelRequest, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    PARCEL_SERVICE_URL + "/api/v1/parcels",
                    HttpMethod.POST,
                    entity,
                    Map.class);
            return ResponseEntity.ok(response.getBody());
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Parcel service unavailable");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    @GetMapping("/parcels/{parcelId}/track")
    public ResponseEntity<Map<String, Object>> trackParcel(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @PathVariable String parcelId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    PARCEL_SERVICE_URL + "/api/v1/parcels/" + parcelId + "/track",
                    HttpMethod.GET,
                    entity,
                    Map.class);
            return ResponseEntity.ok(response.getBody());
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Failed to track parcel");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    @GetMapping("/parcels")
    public ResponseEntity<Map<String, Object>> getParcels(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    PARCEL_SERVICE_URL + "/api/v1/parcels?page=" + page + "&size=" + size,
                    HttpMethod.GET,
                    entity,
                    Map.class);
            return ResponseEntity.ok(response.getBody());
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Failed to fetch parcels");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    // =====================================================
    // RIDE ENDPOINTS
    // =====================================================

    @PostMapping("/rides")
    public ResponseEntity<Map<String, Object>> createRide(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @RequestBody Map<String, Object> rideRequest) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(rideRequest, headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    RIDE_SERVICE_URL + "/api/v1/rides",
                    HttpMethod.POST,
                    entity,
                    Map.class);
            return ResponseEntity.ok(response.getBody());
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Ride service unavailable");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    @GetMapping("/rides/{rideId}")
    public ResponseEntity<Map<String, Object>> getRide(
            @RequestHeader("X-Tenant-ID") String tenantId,
            @PathVariable String rideId) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            HttpEntity<String> entity = new HttpEntity<>(headers);

            ResponseEntity<Map> response = restTemplate.exchange(
                    RIDE_SERVICE_URL + "/api/v1/rides/" + rideId,
                    HttpMethod.GET,
                    entity,
                    Map.class);
            @SuppressWarnings("unchecked")
            Map<String, Object> responseBody = (Map<String, Object>) response.getBody();
            return ResponseEntity.ok(responseBody);
        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("status", "error");
            errorResponse.put("message", "Failed to fetch ride");
            return ResponseEntity.status(503).body(errorResponse);
        }
    }

    // =====================================================
    // DASHBOARD ENDPOINTS
    // =====================================================

    @GetMapping("/dashboard/stats")
    public ResponseEntity<Map<String, Object>> getDashboardStats(
            @RequestHeader("X-Tenant-ID") String tenantId) {

        Map<String, Object> dashboardStats = new HashMap<>();

        try {
            // Get wallet stats
            HttpHeaders headers = new HttpHeaders();
            headers.set(TENANT_HEADER, tenantId);
            // Note: entity would be used for actual service calls
            // HttpEntity<String> entity = new HttpEntity<>(headers);

            // Aggregate stats from multiple services
            Map<String, Object> stats = new HashMap<>();
            stats.put("totalWallets", 0);
            stats.put("totalParcels", 0);
            stats.put("totalRides", 0);
            stats.put("activeDrivers", 0);
            stats.put("revenue", 0.0);

            dashboardStats.put("status", "success");
            dashboardStats.put("data", stats);

        } catch (Exception e) {
            dashboardStats.put("status", "error");
            dashboardStats.put("message", "Failed to fetch dashboard stats");
        }

        return ResponseEntity.ok(dashboardStats);
    }

    // =====================================================
    // HEALTH CHECK
    // =====================================================

    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        Map<String, Object> health = new HashMap<>();
        health.put("status", "UP");
        health.put("timestamp", System.currentTimeMillis());
        health.put("services", Map.of(
                "auth-service", checkServiceHealth(AUTH_SERVICE_URL + "/api/test/health"),
                "wallet-service", "UNKNOWN",
                "parcel-service", "UNKNOWN",
                "ride-service", checkServiceHealth(RIDE_SERVICE_URL + "/actuator/health")));
        return ResponseEntity.ok(health);
    }

    private String checkServiceHealth(String url) {
        try {
            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);
            return response.getStatusCode().is2xxSuccessful() ? "UP" : "DOWN";
        } catch (Exception e) {
            return "DOWN";
        }
    }
}
