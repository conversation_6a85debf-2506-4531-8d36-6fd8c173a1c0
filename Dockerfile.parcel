# Dockerfile for TecnoDrive Parcel Service
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY requirements.txt* ./
RUN pip install --no-cache-dir fastapi uvicorn[standard] python-multipart

# Copy application code
COPY parcel_service.py ./

# Create non-root user
RUN useradd -m -u 1000 parceluser && chown -R parceluser:parceluser /app
USER parceluser

# Expose port
EXPOSE 8087

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8087/health || exit 1

# Run the application
CMD ["python", "-m", "uvicorn", "parcel_service:app", "--host", "0.0.0.0", "--port", "8087"]
