"""
خدمة الطرود - TecnoDrive Parcel Service
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from datetime import datetime
from typing import List, Dict, Any
import uuid

app = FastAPI(
    title="TecnoDrive Parcel Service",
    description="خدمة إدارة الطرود والتوصيل",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mock database
parcels_db = [
    {
        "id": "PCL-001",
        "barcode": "123456789012",
        "sender_name": "أحمد محمد",
        "receiver_name": "خالد علي",
        "sender_address": "شارع بغداد، صنعاء",
        "receiver_address": "شارع الزبيري، صنعاء",
        "weight_kg": 2.5,
        "status": "IN_TRANSIT",
        "priority": "MEDIUM",
        "estimated_cost": 45.50,
        "created_at": "2025-01-29T09:30:00",
        "estimated_delivery": "2025-01-29T16:00:00"
    },
    {
        "id": "PCL-002",
        "barcode": "123456789013",
        "sender_name": "فاطمة أحمد",
        "receiver_name": "سارة محمد",
        "sender_address": "شارع الستين، صنعاء",
        "receiver_address": "شارع الحصبة، صنعاء",
        "weight_kg": 1.2,
        "status": "DELIVERED",
        "priority": "HIGH",
        "estimated_cost": 32.00,
        "created_at": "2025-01-29T08:15:00",
        "delivered_at": "2025-01-29T12:30:00"
    },
    {
        "id": "PCL-003",
        "barcode": "123456789014",
        "sender_name": "محمد علي",
        "receiver_name": "عبدالله حسن",
        "sender_address": "شارع الزبيري، صنعاء",
        "receiver_address": "شارع بغداد، صنعاء",
        "weight_kg": 5.0,
        "status": "PICKED_UP",
        "priority": "URGENT",
        "estimated_cost": 75.00,
        "created_at": "2025-01-29T11:00:00",
        "estimated_delivery": "2025-01-29T14:00:00"
    }
]

@app.get("/")
async def root():
    return {
        "message": "TecnoDrive Parcel Service is running!",
        "service": "Parcel Management",
        "timestamp": datetime.now().isoformat(),
        "status": "healthy"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "UP",
        "service": "TecnoDrive Parcel Service",
        "timestamp": datetime.now().isoformat(),
        "total_parcels": len(parcels_db)
    }

@app.get("/api/v1/parcels")
async def get_parcels():
    status_summary = {}
    for parcel in parcels_db:
        status = parcel["status"]
        status_summary[status] = status_summary.get(status, 0) + 1
    
    return {
        "parcels": parcels_db,
        "total_count": len(parcels_db),
        "status_summary": status_summary
    }

@app.get("/api/v1/parcels/statistics")
async def get_parcel_statistics():
    total_revenue = sum(parcel["estimated_cost"] for parcel in parcels_db)
    delivered_count = len([p for p in parcels_db if p["status"] == "DELIVERED"])
    in_transit_count = len([p for p in parcels_db if p["status"] == "IN_TRANSIT"])
    pending_pickup_count = len([p for p in parcels_db if p["status"] == "CREATED"])
    
    priority_breakdown = {}
    status_breakdown = {}
    
    for parcel in parcels_db:
        priority = parcel["priority"]
        status = parcel["status"]
        priority_breakdown[priority] = priority_breakdown.get(priority, 0) + 1
        status_breakdown[status] = status_breakdown.get(status, 0) + 1
    
    return {
        "total_parcels": len(parcels_db),
        "delivered_today": delivered_count,
        "in_transit": in_transit_count,
        "pending_pickup": pending_pickup_count,
        "total_revenue": total_revenue,
        "average_delivery_time": "4.2 hours",
        "delivery_success_rate": 98.5,
        "priority_breakdown": priority_breakdown,
        "status_breakdown": status_breakdown
    }

@app.post("/api/v1/parcels")
async def create_parcel(parcel_data: dict):
    new_parcel_id = f"PCL-{str(uuid.uuid4())[:8].upper()}"
    new_parcel = {
        "id": new_parcel_id,
        "barcode": parcel_data.get("barcode", f"BAR{datetime.now().strftime('%Y%m%d%H%M%S')}"),
        "sender_name": parcel_data.get("sender_name", ""),
        "receiver_name": parcel_data.get("receiver_name", ""),
        "sender_address": parcel_data.get("sender_address", ""),
        "receiver_address": parcel_data.get("receiver_address", ""),
        "weight_kg": parcel_data.get("weight_kg", 1.0),
        "status": "CREATED",
        "priority": parcel_data.get("priority", "MEDIUM"),
        "estimated_cost": parcel_data.get("estimated_cost", 35.00),
        "created_at": datetime.now().isoformat(),
        "estimated_delivery": parcel_data.get("estimated_delivery", "")
    }
    
    parcels_db.append(new_parcel)
    
    return {
        "success": True,
        "message": "تم إنشاء الطرد بنجاح",
        "parcel": new_parcel,
        "tracking_url": f"http://localhost:8087/api/v1/parcels/track/{new_parcel_id}"
    }

@app.get("/api/v1/parcels/track/{parcel_id}")
async def track_parcel(parcel_id: str):
    parcel = next((p for p in parcels_db if p["id"] == parcel_id), None)
    if not parcel:
        raise HTTPException(status_code=404, detail="Parcel not found")
    
    tracking_history = [
        {
            "status": "CREATED",
            "location": "مركز الفرز - صنعاء",
            "timestamp": parcel["created_at"],
            "description": "تم إنشاء الطرد وتسجيله في النظام"
        }
    ]
    
    if parcel["status"] in ["PICKED_UP", "IN_TRANSIT", "DELIVERED"]:
        tracking_history.append({
            "status": "PICKED_UP",
            "location": parcel["sender_address"],
            "timestamp": parcel["created_at"],
            "description": "تم استلام الطرد من المرسل"
        })
    
    if parcel["status"] in ["IN_TRANSIT", "DELIVERED"]:
        tracking_history.append({
            "status": "IN_TRANSIT",
            "location": "مركز التوزيع - صنعاء",
            "timestamp": parcel["created_at"],
            "description": "الطرد في الطريق للتوصيل"
        })
    
    if parcel["status"] == "DELIVERED":
        tracking_history.append({
            "status": "DELIVERED",
            "location": parcel["receiver_address"],
            "timestamp": parcel.get("delivered_at", parcel["created_at"]),
            "description": "تم تسليم الطرد بنجاح"
        })
    
    return {
        "parcel_id": parcel_id,
        "current_status": parcel["status"],
        "tracking_history": tracking_history,
        "estimated_delivery": parcel.get("estimated_delivery", ""),
        "delivery_address": parcel["receiver_address"],
        "parcel_details": parcel
    }

@app.get("/api/v1/parcels/{parcel_id}")
async def get_parcel(parcel_id: str):
    parcel = next((p for p in parcels_db if p["id"] == parcel_id), None)
    if not parcel:
        raise HTTPException(status_code=404, detail="Parcel not found")
    return parcel

@app.put("/api/v1/parcels/{parcel_id}/status")
async def update_parcel_status(parcel_id: str, status_data: dict):
    parcel = next((p for p in parcels_db if p["id"] == parcel_id), None)
    if not parcel:
        raise HTTPException(status_code=404, detail="Parcel not found")
    
    new_status = status_data.get("status")
    if new_status:
        parcel["status"] = new_status
        if new_status == "DELIVERED":
            parcel["delivered_at"] = datetime.now().isoformat()
    
    return {
        "success": True,
        "message": f"تم تحديث حالة الطرد إلى {new_status}",
        "parcel": parcel
    }

if __name__ == "__main__":
    uvicorn.run(
        "parcel_service:app",
        host="0.0.0.0",
        port=8087,
        reload=True,
        log_level="info"
    )
