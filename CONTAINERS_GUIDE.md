# 🐳 دليل الحاويات الشامل - TecnoDrive Containers Guide

## 📋 نظرة عامة

هذا الدليل يوضح جميع الحاويات المطلوبة لتشغيل منصة TecnoDrive مع تعليمات التشغيل المرحلي.

## 🟢 **المرحلة الأولى: الحاويات الأساسية (مطلوبة)**

### 🗄️ **قواعد البيانات والتخزين**
```bash
# تشغيل قواعد البيانات فقط
docker-compose up -d postgres redis
```

| الحاوية | الصورة | المنفذ | الحالة |
|---------|--------|--------|-------|
| `postgres-tecno` | `postgres:15-alpine` | 5432 | ✅ جاهز |
| `tecnodrive-redis` | `redis:7-alpine` | 6379 | ✅ جاهز |

### 🔧 **الخدمات المصغرة الأساسية**
```bash
# تشغيل الخدمات الأساسية
docker-compose up -d auth-service user-service ride-service payment-service parcel-service
```

| الحاوية | الصورة | المنفذ | الحالة |
|---------|--------|--------|-------|
| `tecnodrive-auth` | `nginx:alpine` | 8081 | ✅ جاهز |
| `tecnodrive-user` | `nginx:alpine` | 8083 | ✅ جاهز |
| `tecnodrive-ride` | `nginx:alpine` | 8082 | ✅ جاهز |
| `tecnodrive-payment` | `nginx:alpine` | 8086 | ✅ جاهز |
| `tecnodrive-parcel` | Custom Python | 8087 | ✅ **جديد!** |

### 🌐 **البنية التحتية**
```bash
# تشغيل البنية التحتية
docker-compose up -d eureka api-gateway
```

| الحاوية | الصورة | المنفذ | الحالة |
|---------|--------|--------|-------|
| `tecnodrive-eureka` | `nginx:alpine` | 8761 | ✅ جاهز |
| `tecnodrive-api-gateway` | Custom Build | 8080 | ⚠️ يحتاج بناء |

## 🟡 **المرحلة الثانية: أدوات المراقبة (اختيارية)**

### 📊 **أدوات الإدارة والمراقبة**
```bash
# تشغيل أدوات المراقبة
docker-compose up -d pgadmin redis-insight db-backup
```

| الحاوية | الصورة | المنفذ | الوصف |
|---------|--------|--------|-------|
| `pgadmin-tecno` | `dpage/pgadmin4:latest` | 5050 | إدارة PostgreSQL |
| `redis-insight-tecno` | `redislabs/redisinsight:latest` | 8001 | مراقبة Redis |
| `db-backup-tecno` | `postgres:15-alpine` | - | نسخ احتياطية |

### 🔐 **بيانات الدخول**
- **pgAdmin**: <EMAIL> / admin123
- **Redis Insight**: بدون كلمة مرور

## 🔵 **المرحلة الثالثة: الخدمات المتقدمة (للنظام الكامل)**

### 🚗 **خدمات إضافية**
```bash
# تشغيل الخدمات الإضافية
docker-compose up -d fleet-service location-service
```

| الحاوية | الصورة | المنفذ | الوصف |
|---------|--------|--------|-------|
| `tecnodrive-fleet` | `nginx:alpine` | 8084 | إدارة الأسطول |
| `tecnodrive-location` | `nginx:alpine` | 8085 | خدمات الموقع |

### 💼 **خدمات الأعمال المتقدمة**
```bash
# تشغيل خدمات الأعمال المتقدمة
docker-compose up -d hr-service financial-service wallet-service live-operations-service operations-management-service trip-tracking-service demand-analysis-service
```

| الحاوية | المنفذ | الوصف | الحالة |
|---------|--------|-------|-------|
| `tecnodrive-hr` | 8097 | إدارة الموارد البشرية | ⚠️ يحتاج بناء |
| `tecnodrive-financial` | 8098 | الخدمات المالية | ⚠️ يحتاج بناء |
| `tecnodrive-wallet` | 8099 | إدارة المحافظ | ⚠️ يحتاج بناء |
| `tecnodrive-live-ops` | 8100 | العمليات المباشرة | ⚠️ يحتاج بناء |
| `tecnodrive-operations` | 8101 | إدارة العمليات | ⚠️ يحتاج بناء |
| `tecnodrive-tracking` | 8102 | تتبع الرحلات | ⚠️ يحتاج بناء |
| `tecnodrive-demand` | 8103 | تحليل الطلب | ⚠️ يحتاج بناء |

## 🚀 **تعليمات التشغيل السريع**

### 1. **التشغيل الأساسي (الحد الأدنى)**
```bash
# الانتقال إلى مجلد التكوين
cd config

# تشغيل الخدمات الأساسية فقط
docker-compose up -d postgres redis auth-service user-service ride-service payment-service

# التحقق من الحالة
docker-compose ps
```

### 2. **التشغيل الكامل مع خدمة الطرود**
```bash
# تشغيل النظام الأساسي + خدمة الطرود
docker-compose up -d postgres redis auth-service user-service ride-service payment-service parcel-service

# تشغيل أدوات المراقبة
docker-compose up -d pgadmin redis-insight
```

### 3. **التشغيل الشامل (جميع الخدمات)**
```bash
# تشغيل جميع الخدمات
docker-compose up -d

# مراقبة السجلات
docker-compose logs -f
```

## 🔍 **فحص الخدمات**

### ✅ **URLs للاختبار**
| الخدمة | URL | الوصف |
|--------|-----|-------|
| PostgreSQL | `localhost:5432` | قاعدة البيانات |
| Redis | `localhost:6379` | التخزين المؤقت |
| Auth Service | http://localhost:8081 | خدمة المصادقة |
| User Service | http://localhost:8083 | إدارة المستخدمين |
| Ride Service | http://localhost:8082 | إدارة الرحلات |
| Payment Service | http://localhost:8086 | المدفوعات |
| **Parcel Service** | http://localhost:8087 | **خدمة الطرود الجديدة** |
| pgAdmin | http://localhost:5050 | إدارة قاعدة البيانات |
| Redis Insight | http://localhost:8001 | مراقبة Redis |

### 🧪 **اختبار خدمة الطرود**
```bash
# فحص صحة الخدمة
curl http://localhost:8087/health

# قائمة الطرود
curl http://localhost:8087/api/v1/parcels

# إحصائيات الطرود
curl http://localhost:8087/api/v1/parcels/statistics

# Swagger UI
# افتح في المتصفح: http://localhost:8087/docs
```

## 📊 **متطلبات النظام**

### 🖥️ **الحد الأدنى (الخدمات الأساسية)**
- **RAM**: 4 GB
- **CPU**: 2 cores
- **Storage**: 10 GB
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 🚀 **الموصى به (النظام الكامل)**
- **RAM**: 8 GB
- **CPU**: 4 cores
- **Storage**: 20 GB
- **Network**: 100 Mbps

## 🛠️ **استكشاف الأخطاء**

### ❌ **مشاكل شائعة**

#### 1. **فشل في بناء الحاوية**
```bash
# إعادة بناء الحاويات
docker-compose build --no-cache

# تشغيل مع إعادة البناء
docker-compose up -d --build
```

#### 2. **مشاكل في الشبكة**
```bash
# إعادة إنشاء الشبكة
docker-compose down
docker network prune
docker-compose up -d
```

#### 3. **مشاكل في قاعدة البيانات**
```bash
# إعادة تشغيل قاعدة البيانات
docker-compose restart postgres

# فحص السجلات
docker-compose logs postgres
```

## 📝 **ملاحظات مهمة**

### ⚠️ **تحذيرات**
- **خدمات Node.js المتقدمة** تحتاج إلى بناء أولاً
- **API Gateway** يحتاج تكوين إضافي
- **خدمة الطرود** جاهزة للاستخدام فوراً

### ✅ **نصائح**
- ابدأ بالخدمات الأساسية أولاً
- استخدم `docker-compose ps` لمراقبة الحالة
- استخدم `docker-compose logs [service]` لفحص الأخطاء
- احفظ البيانات المهمة قبل إعادة التشغيل

---

## 🎉 **الخلاصة**

**إجمالي الحاويات**: 20+ حاوية
**الحاويات الأساسية**: 7 حاويات
**الحاويات الجاهزة**: 15 حاوية
**الحاويات التي تحتاج بناء**: 8 حاويات

**للبدء السريع**: استخدم الخدمات الأساسية فقط!
